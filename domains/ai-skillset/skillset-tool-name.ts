import type { ILocaleContext } from '@bika/contents/i18n/context';

export const getSkillsetName = (locale: ILocaleContext, key: string) => {
  return getSkillsetNameMap(locale)[key];
};

export const getSkillsetNameMap = (locale: ILocaleContext) => {
  const t = locale.t;
  return {
    // Skillset categories
    bika_app_builder: 'bika-app-builder',
    bika_ai_page: 'bika-ai-page',
    bika_space: 'bika-space',
    bika_database: 'bika-database',
    bika_datasets: 'bika-datasets',
    bika_automation: 'bika-automation',
    bika_image: 'bika-image',
    bika_media: 'bika-media',
    bika_search: 'bika-search',
    bika_research: 'bika-research',
    bika_office: 'bika-office',
    bika_document: 'bika-document',
    debug: 'debug',
    default: 'default',

    // bika-automation tools
    run_automation: 'run_automation',
    get_automation_detail: 'get_automation_detail',

    // bika-research tools
    bika_company_research: 'bika_company_research',

    // bika-space tools
    get_node_info: 'get_node_info',
    list_nodes: 'list_nodes',
    list_users: 'list_users',
    list_members: 'list_members',
    list_teams: 'list_teams',
    list_roles: 'list_roles',

    // bika-office tools
    generate_markdown_document: 'generate_markdown_document',
    generate_slides: 'generate_slides',

    // bika-ai-page tools
    search_image: 'search_image',
    generate_html_page: 'generate_html_page',
    ask_for_apply_ai_page: 'ask_for_apply_ai_page',

    // bika-search tools
    bika_search_pages: 'bika_search_pages',
    bika_search_images: 'bika_search_images',

    // bika-app-builder tools
    'bika-ai-app-builder-planner': 'bika-ai-app-builder-planner',
    'bika-ai-app-builder-engineer': 'bika-ai-app-builder-engineer',
    'bika-ai-app-builder-installer': 'bika-ai-app-builder-installer',

    // bika-database tools
    list_records: 'list_records',
    get_database_detail: 'get_database_detail',
    aggregate_records: 'aggregate_records',
    get_fields_schema: 'get_fields_schema',
    create_record: 'create_record',

    // bika-image tools
    generate_image: 'generate_image',
    image_to_text: 'image_to_text',

    // bika-datasets tools
    companies: 'companies',
    people: 'people',

    // debug tools
    askForConfirmation: 'askForConfirmation',
    getWeatherInformation: 'getWeatherInformation',

    // default tools
    read_node_content: 'read_node_content',

    // bika-document tools
    create_document: t.bika_document.create_document,
  };
};
